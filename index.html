<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VelaSweets - حلويات فاخرة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #8B5CF6;
            --secondary-color: #EC4899;
            --accent-color: #F59E0B;
            --background: #FFFFFF;
            --surface: #F9FAFB;
            --text-primary: #111827;
            --text-secondary: #6B7280;
            --border-color: #E5E7EB;
            --success-color: #10B981;
            --error-color: #EF4444;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --background: #111827;
            --surface: #1F2937;
            --text-primary: #F9FAFB;
            --text-secondary: #D1D5DB;
            --border-color: #374151;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: var(--background);
            color: var(--text-primary);
            line-height: 1.6;
            transition: background-color 0.3s, color 0.3s;
        }

        /* Header Styles */
        header {
            background-color: var(--surface);
            border-bottom: 1px solid var(--border-color);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            transition: all 0.3s;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .header-icons {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        .icon-btn {
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.25rem;
            cursor: pointer;
            position: relative;
            transition: color 0.3s;
        }

        .icon-btn:hover {
            color: var(--primary-color);
        }

        .badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--accent-color);
            color: white;
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 9999px;
            font-weight: bold;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 8rem 2rem 4rem;
            text-align: center;
            margin-top: 70px;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: fadeInUp 0.8s;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: fadeInUp 0.8s 0.2s both;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            animation: fadeInUp 0.8s 0.4s both;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: white;
            color: var(--primary-color);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background-color: white;
            color: var(--primary-color);
        }

        /* Products Section */
        .products-section {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: var(--text-primary);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background-color: var(--surface);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .product-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            background-color: var(--border-color);
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-name {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .product-price {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .product-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .add-to-cart {
            flex: 1;
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .add-to-cart:hover {
            background-color: var(--secondary-color);
        }

        .favorite-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s;
        }

        .favorite-btn.active {
            color: var(--error-color);
        }

        /* About Section */
        .about-section {
            background-color: var(--surface);
            padding: 4rem 2rem;
        }

        .about-cards {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .about-card {
            background-color: var(--background);
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s;
        }

        .about-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .about-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .about-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .about-description {
            color: var(--text-secondary);
        }

        /* Contact Section */
        .contact-section {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
        }

        .contact-info h3 {
            font-size: 1.75rem;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            color: var(--text-secondary);
        }

        .contact-item i {
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background-color: var(--surface);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s;
        }

        .social-link:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .contact-form {
            background-color: var(--surface);
            padding: 2rem;
            border-radius: 12px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--background);
            color: var(--text-primary);
            font-family: inherit;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        /* Footer */
        footer {
            background-color: var(--surface);
            padding: 3rem 2rem 1rem;
            border-top: 1px solid var(--border-color);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-column h4 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .footer-column p,
        .footer-column a {
            color: var(--text-secondary);
            text-decoration: none;
            line-height: 2;
            transition: color 0.3s;
        }

        .footer-column a:hover {
            color: var(--primary-color);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            width: 400px;
            height: 100%;
            background-color: var(--background);
            box-shadow: var(--shadow-lg);
            z-index: 2000;
            transition: transform 0.3s;
            overflow-y: auto;
        }

        .sidebar-left {
            left: 0;
            transform: translateX(-100%);
        }

        .sidebar-right {
            right: 0;
            transform: translateX(100%);
        }

        .sidebar.active {
            transform: translateX(0);
        }

        .sidebar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-sidebar {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-primary);
        }

        .sidebar-content {
            padding: 1.5rem;
        }

        .cart-item,
        .favorite-item {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background-color: var(--surface);
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            background-color: var(--border-color);
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .item-price {
            color: var(--primary-color);
            font-weight: 500;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .quantity-btn {
            background-color: var(--border-color);
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quantity-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .quantity {
            font-weight: 500;
        }

        .remove-item {
            background: none;
            border: none;
            color: var(--error-color);
            cursor: pointer;
            font-size: 1.25rem;
        }

        .sidebar-footer {
            position: sticky;
            bottom: 0;
            background-color: var(--background);
            padding: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .empty-message {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 3000;
            align-items: center;
            justify-content: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background-color: var(--background);
            padding: 2rem;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalFadeIn 0.3s;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-primary);
        }

        .success-modal {
            text-align: center;
            padding: 3rem;
        }

        .success-icon {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: 1rem;
        }

        .success-message {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .cash-on-delivery {
            background-color: var(--surface);
            padding: 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1.5rem 0;
            color: var(--text-secondary);
        }

        .cash-on-delivery i {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        /* Product Details Modal */
        .product-details-modal .modal-content {
            max-width: 800px;
        }

        .product-details-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .product-details-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 8px;
            background-color: var(--border-color);
        }

        .product-details-info h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .product-details-price {
            font-size: 2rem;
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .product-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .product-options {
            margin-bottom: 2rem;
        }

        .product-options h4 {
            margin-bottom: 1rem;
        }

        .option-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .option-btn {
            padding: 0.5rem 1.5rem;
            border: 2px solid var(--border-color);
            background-color: var(--background);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: inherit;
        }

        .option-btn:hover {
            border-color: var(--primary-color);
        }

        .option-btn.selected {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Info Pages */
        .info-modal .modal-content {
            max-width: 700px;
        }

        .info-content {
            line-height: 1.8;
        }

        .info-content h3 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .info-content p {
            margin-bottom: 1rem;
            color: var(--text-secondary);
        }

        .info-content ul {
            margin-right: 2rem;
            margin-bottom: 1rem;
        }

        .info-content ul li {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .faq-item {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background-color: var(--surface);
            border-radius: 8px;
        }

        .faq-question {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .faq-answer {
            color: var(--text-secondary);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .header-container {
                padding: 1rem;
            }

            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                width: 100%;
                max-width: 300px;
                margin: 0 auto;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }

            .contact-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .sidebar {
                width: 100%;
            }

            .product-details-container {
                grid-template-columns: 1fr;
            }

            .product-details-image {
                height: 300px;
            }
        }

        /* Loading Spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-primary);
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            .nav-links {
                position: fixed;
                top: 70px;
                left: 0;
                right: 0;
                background-color: var(--surface);
                flex-direction: column;
                padding: 1rem;
                transform: translateY(-100%);
                transition: transform 0.3s;
                border-bottom: 1px solid var(--border-color);
            }

            .nav-links.active {
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                <i class="ri-menu-line"></i>
            </button>
            <nav>
                <ul class="nav-links" id="navLinks">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#products">المنتجات</a></li>
                    <li><a href="#" onclick="showInfoModal('faq')">مركز المعلومات</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <button class="icon-btn" onclick="toggleTheme()">
                    <i class="ri-moon-line" id="themeIcon"></i>
                </button>
                <button class="icon-btn" onclick="toggleFavorites()">
                    <i class="ri-heart-line"></i>
                    <span class="badge" id="favoritesCount">0</span>
                </button>
                <button class="icon-btn" onclick="toggleCart()">
                    <i class="ri-shopping-cart-line"></i>
                    <span class="badge" id="cartCount">0</span>
                </button>
                <button class="icon-btn">
                    <i class="ri-user-line"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <h1>VelaSweets</h1>
        <p>حلويات فاخرة بلمسة عصرية</p>
        <div class="hero-buttons">
            <a href="#products" class="btn btn-primary">شاهد المنتجات</a>
            <a href="#about" class="btn btn-secondary">من نحن</a>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section" id="products">
        <h2 class="section-title">منتجاتنا</h2>
        <div class="products-grid" id="productsGrid">
            <!-- Products will be loaded here -->
        </div>
    </section>

    <!-- About Section -->
    <section class="about-section" id="about">
        <h2 class="section-title">من نحن</h2>
        <div class="about-cards">
            <div class="about-card">
                <div class="about-icon">🏆</div>
                <h3 class="about-title">جودة عالية</h3>
                <p class="about-description">نستخدم أجود المكونات الطبيعية لضمان طعم لا يُنسى</p>
            </div>
            <div class="about-card">
                <div class="about-icon">🚚</div>
                <h3 class="about-title">توصيل سريع</h3>
                <p class="about-description">نوصل طلباتك بسرعة للحفاظ على طزاجة المنتجات</p>
            </div>
            <div class="about-card">
                <div class="about-icon">📞</div>
                <h3 class="about-title">خدمة ممتازة</h3>
                <p class="about-description">فريق دعم متخصص لخدمتك على مدار الساعة</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section" id="contact">
        <h2 class="section-title">تواصل معنا</h2>
        <div class="contact-container">
            <div class="contact-info">
                <h3>معلومات الاتصال</h3>
                <div class="contact-item">
                    <i class="ri-phone-line"></i>
                    <span>07800000000</span>
                </div>
                <div class="contact-item">
                    <i class="ri-mail-line"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="ri-map-pin-line"></i>
                    <span>البصرة - العراق</span>
                </div>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-instagram-line"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-tiktok-fill"></i>
                    </a>
                </div>
            </div>
            <form class="contact-form" onsubmit="handleContactSubmit(event)">
                <div class="form-group">
                    <label for="name">الاسم</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="message">الرسالة</label>
                    <textarea id="message" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">إرسال</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <h4>VelaSweets</h4>
                <p>حلويات فاخرة بلمسة عصرية</p>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-instagram-line"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-tiktok-fill"></i>
                    </a>
                </div>
            </div>
            <div class="footer-column">
                <h4>روابط سريعة</h4>
                <a href="#home">الرئيسية</a><br>
                <a href="#products">المنتجات</a><br>
                <a href="#about">من نحن</a><br>
                <a href="#contact">تواصل معنا</a><br>
                <a href="#" onclick="showInfoModal('privacy')">سياسة الخصوصية</a><br>
                <a href="#" onclick="showInfoModal('terms')">الشروط والأحكام</a>
            </div>
            <div class="footer-column">
                <h4>معلومات الاتصال</h4>
                <p><i class="ri-phone-line"></i> 07800000000</p>
                <p><i class="ri-mail-line"></i> <EMAIL></p>
                <p><i class="ri-map-pin-line"></i> البصرة - العراق</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>جميع الحقوق محفوظة © 2026 VelaSweets</p>
        </div>
    </footer>

    <!-- Cart Sidebar -->
    <div class="sidebar sidebar-left" id="cartSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">السلة</h3>
            <button class="close-sidebar" onclick="toggleCart()">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar-content" id="cartContent">
            <!-- Cart items will be loaded here -->
        </div>
        <div class="sidebar-footer" id="cartFooter" style="display: none;">
            <div class="total">
                <span>المجموع:</span>
                <span id="cartTotal">0 دينار</span>
            </div>
            <button class="btn btn-primary" style="width: 100%;" onclick="showOrderModal()">
                اطلب الآن
            </button>
        </div>
    </div>

    <!-- Favorites Sidebar -->
    <div class="sidebar sidebar-right" id="favoritesSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">المفضلة</h3>
            <button class="close-sidebar" onclick="toggleFavorites()">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar-content" id="favoritesContent">
            <!-- Favorite items will be loaded here -->
        </div>
        <div class="sidebar-footer" id="favoritesFooter" style="display: none;">
            <button class="btn btn-secondary" style="width: 100%;" onclick="clearFavorites()">
                مسح الكل
            </button>
        </div>
    </div>

    <!-- Order Modal -->
    <div class="modal" id="orderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إتمام الطلب</h3>
                <button class="close-modal" onclick="closeModal('orderModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <form onsubmit="handleOrderSubmit(event)">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل</label>
                    <input type="text" id="fullName" required>
                </div>
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" required>
                </div>
                <div class="form-group">
                    <label for="province">المحافظة</label>
                    <select id="province" required>
                        <option value="">اختر المحافظة</option>
                        <option value="البصرة">البصرة</option>
                        <option value="بغداد">بغداد</option>
                        <option value="الموصل">الموصل</option>
                        <option value="النجف">النجف</option>
                        <option value="كربلاء">كربلاء</option>
                        <option value="بابل">بابل</option>
                        <option value="ديالى">ديالى</option>
                        <option value="ذي قار">ذي قار</option>
                        <option value="ميسان">ميسان</option>
                        <option value="واسط">واسط</option>
                        <option value="المثنى">المثنى</option>
                        <option value="القادسية">القادسية</option>
                        <option value="الأنبار">الأنبار</option>
                        <option value="صلاح الدين">صلاح الدين</option>
                        <option value="كركوك">كركوك</option>
                        <option value="السليمانية">السليمانية</option>
                        <option value="أربيل">أربيل</option>
                        <option value="دهوك">دهوك</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="address">العنوان التفصيلي</label>
                    <textarea id="address" required></textarea>
                </div>
                <div class="cash-on-delivery">
                    <i class="ri-money-dollar-circle-line"></i>
                    <span>الدفع عند الاستلام</span>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    تأكيد الطلب
                </button>
            </form>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="successModal">
        <div class="modal-content success-modal">
            <div class="success-icon">✓</div>
            <h3 class="success-message">تم الطلب بنجاح!</h3>
            <p>شكراً لك، سيتم التواصل معك قريباً لتأكيد الطلب</p>
            <button class="btn btn-primary" onclick="closeSuccessModal()">
                العودة للمتجر
            </button>
        </div>
    </div>

    <!-- Product Details Modal -->
    <div class="modal product-details-modal" id="productDetailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">تفاصيل المنتج</h3>
                <button class="close-modal" onclick="closeModal('productDetailsModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div id="productDetailsContent">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="modal info-modal" id="infoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="infoModalTitle"></h3>
                <button class="close-modal" onclick="closeModal('infoModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="info-content" id="infoModalContent">
                <!-- Info content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Products Data
        const products = [
            {
                id: 1,
                name: 'جوزية',
                price: 7000,
                image: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="280" height="250" viewBox="0 0 280 250"%3E%3Crect width="280" height="250" fill="%23E5E7EB"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%236B7280" font-family="Arial" font-size="16"%3Eجوزية%3C/text%3E%3C/svg%3E',
                description: 'قطع جوزية محشية ومغطاة بطبقة شوكولاتة فاخرة',
                options: ['الكراميل', 'الشوكولاتة', 'البستاشيو']
            },
            {
                id: 2,
                name: 'مادلين كيك',
                price: 4500,
                image: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="280" height="250" viewBox="0 0 280 250"%3E%3Crect width="280" height="250" fill="%23E5E7EB"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%236B7280" font-family="Arial" font-size="16"%3Eمادلين كيك%3C/text%3E%3C/svg%3E',
                description: 'كيك ناعم ومميز مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص',
                options: ['بالبندق']
            },
            {
                id: 3,
                name: 'حلى ڤيلا',
                price: 6000,
                image: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="280" height="250" viewBox="0 0 280 250"%3E%3Crect width="280" height="250" fill="%23E5E7EB"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%236B7280" font-family="Arial" font-size="16"%3Eحلى ڤيلا%3C/text%3E%3C/svg%3E',
                description: 'حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش',
                options: ['بالفول السوداني']
            }
        ];

        // State Management
        let cart = [];
        let favorites = [];
        let selectedOptions = {};

        // Theme Toggle
        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            document.getElementById('themeIcon').className = newTheme === 'dark' ? 'ri-sun-line' : 'ri-moon-line';
            localStorage.setItem('theme', newTheme);
        }

        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('active');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadFromLocalStorage();
            renderProducts();
            updateCartUI();
            updateFavoritesUI();
            
            // Check theme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                document.getElementById('themeIcon').className = 'ri-sun-line';
            }
        });

        // Render Products
        function renderProducts() {
            const productsGrid = document.getElementById('productsGrid');
            productsGrid.innerHTML = products.map(product => `
                <div class="product-card" onclick="showProductDetails(${product.id})">
                    <img src="${product.image}" alt="${product.name}" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">${product.name}</h3>
                        <div class="product-price">${product.price.toLocaleString()} دينار</div>
                        <div class="product-actions">
                            <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${product.id})">
                                أضف للسلة
                            </button>
                            <button class="favorite-btn ${favorites.includes(product.id) ? 'active' : ''}" 
                                    onclick="event.stopPropagation(); toggleFavorite(${product.id})">
                                <i class="ri-heart-${favorites.includes(product.id) ? 'fill' : 'line'}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Show Product Details
        function showProductDetails(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            selectedOptions[productId] = selectedOptions[productId] || product.options[0];

            const content = `
                <div class="product-details-container">
                    <img src="${product.image}" alt="${product.name}" class="product-details-image">
                    <div class="product-details-info">
                        <h2>${product.name}</h2>
                        <div class="product-details-price">${product.price.toLocaleString()} دينار</div>
                        <p class="product-description">${product.description}</p>
                        <div class="product-options">
                            <h4>خيارات النكهة:</h4>
                            <div class="option-buttons">
                                ${product.options.map(option => `
                                    <button class="option-btn ${selectedOptions[productId] === option ? 'selected' : ''}"
                                            onclick="selectOption(${productId}, '${option}')">
                                        ${option}
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                            <button class="btn btn-primary" style="flex: 1;" onclick="addToCart(${productId}); closeModal('productDetailsModal');">
                                أضف للسلة
                            </button>
                            <button class="favorite-btn ${favorites.includes(productId) ? 'active' : ''}" 
                                    style="font-size: 2rem;" onclick="toggleFavorite(${productId})">
                                <i class="ri-heart-${favorites.includes(productId) ? 'fill' : 'line'}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('productDetailsContent').innerHTML = content;
            document.getElementById('productDetailsModal').classList.add('active');
        }

        // Select Option
        function selectOption(productId, option) {
            selectedOptions[productId] = option;
            showProductDetails(productId); // Re-render to update selection
        }

        // Cart Functions
        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity++;
            } else {
                cart.push({
                    ...product,
                    quantity: 1,
                    selectedOption: selectedOptions[productId] || product.options[0]
                });
            }

            saveToLocalStorage();
            updateCartUI();
            showNotification('تمت الإضافة إلى السلة');
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            saveToLocalStorage();
            updateCartUI();
        }

        function updateQuantity(productId, change) {
            const item = cart.find(item => item.id === productId);
            if (item) {
                item.quantity += change;
                if (item.quantity <= 0) {
                    removeFromCart(productId);
                } else {
                    saveToLocalStorage();
                    updateCartUI();
                }
            }
        }

        function updateCartUI() {
            const cartContent = document.getElementById('cartContent');
            const cartFooter = document.getElementById('cartFooter');
            const cartCount = document.getElementById('cartCount');

            if (cart.length === 0) {
                cartContent.innerHTML = `
                    <div class="empty-message">
                        <div class="empty-icon">🛒</div>
                        <p>السلة فارغة</p>
                    </div>
                `;
                cartFooter.style.display = 'none';
            } else {
                cartContent.innerHTML = cart.map(item => `
                    <div class="cart-item">
                        <img src="${item.image}" alt="${item.name}" class="item-image">
                        <div class="item-details">
                            <div class="item-name">${item.name}</div>
                            <div class="item-price">${item.price.toLocaleString()} دينار</div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">${item.selectedOption}</div>
                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                                <span class="quantity">${item.quantity}</span>
                                <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                            </div>
                        </div>
                        <button class="remove-item" onclick="removeFromCart(${item.id})">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                `).join('');
                cartFooter.style.display = 'block';
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            document.getElementById('cartTotal').textContent = `${total.toLocaleString()} دينار`;
            cartCount.textContent = cart.reduce((sum, item) => sum + item.quantity, 0);
        }

        function toggleCart() {
            const cartSidebar = document.getElementById('cartSidebar');
            const favoritesSidebar = document.getElementById('favoritesSidebar');
            
            // Close favorites if open
            favoritesSidebar.classList.remove('active');
            
            cartSidebar.classList.toggle('active');
        }

        // Favorites Functions
        function toggleFavorite(productId) {
            const index = favorites.indexOf(productId);
            if (index > -1) {
                favorites.splice(index, 1);
            } else {
                favorites.push(productId);
            }
            
            saveToLocalStorage();
            updateFavoritesUI();
            renderProducts(); // Update heart icons
        }

        function updateFavoritesUI() {
            const favoritesContent = document.getElementById('favoritesContent');
            const favoritesFooter = document.getElementById('favoritesFooter');
            const favoritesCount = document.getElementById('favoritesCount');

            if (favorites.length === 0) {
                favoritesContent.innerHTML = `
                    <div class="empty-message">
                        <div class="empty-icon">❤️</div>
                        <p>لا توجد منتجات مفضلة</p>
                    </div>
                `;
                favoritesFooter.style.display = 'none';
            } else {
                const favoriteProducts = products.filter(p => favorites.includes(p.id));
                favoritesContent.innerHTML = favoriteProducts.map(item => `
                    <div class="favorite-item">
                        <img src="${item.image}" alt="${item.name}" class="item-image">
                        <div class="item-details">
                            <div class="item-name">${item.name}</div>
                            <div class="item-price">${item.price.toLocaleString()} دينار</div>
                            <button class="btn btn-primary" style="margin-top: 0.5rem;" 
                                    onclick="addToCart(${item.id}); toggleFavorite(${item.id});">
                                أضف للسلة
                            </button>
                        </div>
                        <button class="remove-item" onclick="toggleFavorite(${item.id})">
                            <i class="ri-heart-fill"></i>
                        </button>
                    </div>
                `).join('');
                favoritesFooter.style.display = 'block';
            }

            favoritesCount.textContent = favorites.length;
        }

        function toggleFavorites() {
            const favoritesSidebar = document.getElementById('favoritesSidebar');
            const cartSidebar = document.getElementById('cartSidebar');
            
            // Close cart if open
            cartSidebar.classList.remove('active');
            
            favoritesSidebar.classList.toggle('active');
        }

        function clearFavorites() {
            favorites = [];
            saveToLocalStorage();
            updateFavoritesUI();
            renderProducts();
        }

        // Modal Functions
        function showOrderModal() {
            if (cart.length === 0) {
                showNotification('السلة فارغة');
                return;
            }
            document.getElementById('orderModal').classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        function handleOrderSubmit(event) {
            event.preventDefault();
            
            const orderData = {
                fullName: document.getElementById('fullName').value,
                phone: document.getElementById('phone').value,
                province: document.getElementById('province').value,
                address: document.getElementById('address').value,
                items: cart,
                total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                date: new Date().toISOString()
            };

            // Save order
            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            orders.push(orderData);
            localStorage.setItem('orders', JSON.stringify(orders));

            // Clear cart
            cart = [];
            saveToLocalStorage();
            updateCartUI();

            // Show success
            closeModal('orderModal');
            document.getElementById('successModal').classList.add('active');
        }

        function closeSuccessModal() {
            closeModal('successModal');
            toggleCart(); // Close cart sidebar
        }

        function handleContactSubmit(event) {
            event.preventDefault();
            showNotification('تم إرسال رسالتك بنجاح');
            event.target.reset();
        }

        // Info Modal
        function showInfoModal(type) {
            const modal = document.getElementById('infoModal');
            const title = document.getElementById('infoModalTitle');
            const content = document.getElementById('infoModalContent');

            switch(type) {
                case 'faq':
                    title.textContent = 'الأسئلة الشائعة';
                    content.innerHTML = `
                        <div class="faq-item">
                            <div class="faq-question">س: كيف يمكنني طلب منتجاتكم؟</div>
                            <div class="faq-answer">ج: يمكنك الطلب من خلال موقعنا الإلكتروني، بإضافة المنتجات إلى السلة ثم إتمام الطلب بعد التأكيد.</div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">س: كم تستغرق مدة التوصيل؟</div>
                            <div class="faq-answer">ج: تختلف مدة التوصيل حسب المحافظة:<br>
                            • البصرة: من نفس اليوم إلى 24 ساعة.<br>
                            • باقي المحافظات: من 2 إلى 3 أيام حسب المسافة.</div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">س: هل يمكنني إلغاء أو تعديل طلبي؟</div>
                            <div class="faq-answer">ج: نعم، يمكن الإلغاء أو التعديل خلال ساعة واحدة من تأكيد الطلب. بعد مرور الساعة يبدأ التحضير، ولا يُمكن التعديل أو الإلغاء بعد ذلك.</div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">س: ما هي طرق الدفع المتاحة؟</div>
                            <div class="faq-answer">ج: حاليًا نقبل الدفع عند الاستلام فقط. نعمل على توفير خيارات دفع إلكتروني قريبًا بإذن الله.</div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">س: هل تقدمون حلويات للمناسبات الخاصة؟</div>
                            <div class="faq-answer">ج: لا نقدم منتجات مخصصة للمناسبات حالياً، لكن يمكنك اختيار ما يناسبك من تشكيلتنا الجاهزة المناسبة للتجمعات الصغيرة.</div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">س: كم تبقى منتجاتكم طازجة؟</div>
                            <div class="faq-answer">ج: تبقى منتجاتنا طازجة لمدة 3 إلى 5 أيام إذا حُفظت في مكان بارد وجاف. للحصول على أفضل طعم، ننصح باستهلاكها خلال أول يومين.</div>
                        </div>
                    `;
                    break;

                case 'privacy':
                    title.textContent = 'سياسة الخصوصية';
                    content.innerHTML = `
                        <h3>سياسة الخصوصية</h3>
                        <p>• نحتفظ ببياناتك الشخصية بسرية تامة.</p>
                        <p>• لا نشارك معلوماتك مع أي جهة خارجية.</p>
                        <p>• نستخدم البيانات لتحسين خدماتنا فقط.</p>
                        <p>• يمكنك طلب حذف بياناتك في أي وقت.</p>
                        
                        <h3>تنويه</h3>
                        <p>• جميع منتجاتنا مصنوعة من مكونات طبيعية.</p>
                        <p>• قد تحتوي على مكسرات أو مواد مسببة للحساسية.</p>
                        <p>• يرجى إبلاغنا عن أي حساسية غذائية عند الطلب.</p>
                        <p>• هذه الشروط قابلة للتعديل مع إعلام الزبائن.</p>
                    `;
                    break;

                case 'terms':
                    title.textContent = 'الشروط والأحكام';
                    content.innerHTML = `
                        <h3>شروط الطلب</h3>
                        <p>• نبدأ بتحضير الطلب بعد تأكيد الزبون والموافقة على الشروط.</p>
                        <p>• جميع الطلبات تُحضّر حسب الطلب لضمان الطزاجة والجودة.</p>
                        <p>• يُفضل إرسال الطلب بوقت كافٍ لضمان توفر المنتج.</p>
                        <p>• لا يمكن إلغاء أو تعديل الطلب بعد بدء التحضير إلا باتفاق الطرفين.</p>
                        <p>• الطلبات تُنفذ حسب وقت استلامها.</p>

                        <h3>شروط التوصيل</h3>
                        <p>• تُضاف رسوم التوصيل إلى قيمة الطلب.</p>
                        <p>• يجب تواجد أحد لاستلام الطلب في الوقت المحدد.</p>
                        <p>• في حالة عدم التواجد، يُعاد الطلب ويُخصم فقط مبلغ رسوم التوصيل إذا تم إعلام الزبون بذلك مسبقًا.</p>
                        <p>• لا نتحمل مسؤولية التأخير الناتج عن ظروف خارجة عن إرادتنا كالأحوال الجوية أو الطرق.</p>

                        <h3>شروط الإرجاع والاستبدال</h3>
                        <p>• لا يُقبل الإرجاع أو الاستبدال إلا في حالة وجود عيب واضح في المنتج.</p>
                        <p>• يُفحص المنتج فور التسليم بحضور المندوب والزبون.</p>
                        <p>• يمكن للزبون التبليغ عن العيب خلال ساعة من وقت الاستلام.</p>
                        <p>• في حال ثبوت وجود عيب، يتم استرجاع المنتج مع تحمل الزبون رسوم التوصيل فقط.</p>
                        <p>• لا نتحمل مسؤولية تلف المنتج بعد التسليم وقبول الزبون له.</p>

                        <h3>الالتزام والتعامل</h3>
                        <p>• إذا رفض الزبون استلام الطلب بدون سبب مشروع، أو امتنع عن الرد على المندوب، أو حظر صفحة التواصل، نحتفظ بحق التوقف عن التعامل معه مستقبلاً.</p>
                        <p>• أي نزاع يُحال إلى الجهات القانونية المختصة للنظر فيه.</p>